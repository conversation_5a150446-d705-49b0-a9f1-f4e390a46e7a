// src/AuthContext.js
import React, { createContext, useState, useContext, useEffect } from 'react';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [userToken, setUserToken] = useState(null); // 存储用户登录后的 token 或标识
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false); // 也可以根据 token 判断是否是管理员

  // 模拟从 localStorage 加载登录状态
  useEffect(() => {
    const token = localStorage.getItem('authToken');
    if (token) {
      setUserToken(token);
      setIsAuthenticated(true);
      // 在实际应用中，你可以根据 token 解码或验证来设置 isAdmin
      // 这里我们为了简单起见，假设只要有 token 就是管理员
      setIsAdmin(true);
    }
  }, []);

  const login = async (username, password) => {
    // 假设 login API 返回 { success: true, token: "...", isAdmin: true/false }
    // 这里我们直接调用后端的 login API
    try {
      // 从 api.js 引入 login 函数
      // const response = await login(username, password); // 注意：login API 返回的是 success 字段，不是 token
      // 为了模拟 token，我们直接设置一个值
      const response = { success: true, message: "Login successful!" }; // 模拟成功响应

      if (response.success) {
        // 假设我们这里生成一个模拟的 token，实际应从后端获取
        const simulatedToken = `simulated_token_${Date.now()}`;
        localStorage.setItem('authToken', simulatedToken);
        setUserToken(simulatedToken);
        setIsAuthenticated(true);
        setIsAdmin(true); // 假设所有登录用户都是管理员
        return true; // 表示登录成功
      } else {
        setIsAuthenticated(false);
        setIsAdmin(false);
        return false; // 表示登录失败
      }
    } catch (error) {
      console.error("Login failed in context:", error);
      setIsAuthenticated(false);
      setIsAdmin(false);
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    setUserToken(null);
    setIsAuthenticated(false);
    setIsAdmin(false);
    // 可以选择跳转到登录页
  };

  return (
    <AuthContext.Provider value={{ userToken, isAuthenticated, isAdmin, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
export { AuthContext };