// src/services/api.js
import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api'; // 后端实际运行端口

// --- 获取课程相关 ---
export const getResources = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/resources`);
    return response.data;
  } catch (error) {
    console.error("API Error in getResources:", error.response?.data || error.message);
    throw error;
  }
};

export const getCourseById = async (id) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/resources/${id}`);
    return response.data;
  } catch (error) {
    console.error(`API Error in getCourseById (${id}):`, error.response?.data || error.message);
    throw error;
  }
};

// 添加一个别名函数，确保兼容性
export const getResourceById = async (id) => {
  return getCourseById(id);
};

// 添加课程
export const addResource = async (courseData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/resources`, courseData);
    return response.data;
  } catch (error) {
    console.error("API Error in addResource:", error.response?.data || error.message);
    throw error;
  }
};

// 更新课程
export const updateResource = async (id, courseData) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/resources/${id}`, courseData);
    return response.data;
  } catch (error) {
    console.error(`API Error in updateResource (${id}):`, error.response?.data || error.message);
    throw error;
  }
};

// 删除课程
export const deleteResource = async (id) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/resources/${id}`);
    return response.data;
  } catch (error) {
    console.error(`API Error in deleteResource (${id}):`, error.response?.data || error.message);
    throw error;
  }
};

// --- 支付和下载相关 ---
export const login = async (username, password) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/login`, { username, password });
    return response.data;
  } catch (error) {
    console.error("API Error in login:", error.response?.data || error.message);
    throw error;
  }
};

export const downloadCourse = async (resourceId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/download/${resourceId}`);
    return response.data;
  } catch (error) {
    console.error(`API Error in downloadCourse (${resourceId}):`, error.response?.data || error.message);
    throw error;
  }
};

export const verifyPaymentAndGetDownload = async (resourceId, paymentId) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/verify-payment/${resourceId}`, { paymentId });
    return response.data;
  } catch (error) {
    console.error(`API Error in verifyPaymentAndGetDownload (${resourceId}):`, error.response?.data || error.message);
    throw error;
  }
};

// 验证微信支付交易单号
export const verifyWechatPayment = async (transactionId, courseId, amount) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/verify-wechat-payment`, {
      transactionId,
      courseId,
      amount
    });
    return response.data;
  } catch (error) {
    console.error(`API Error in verifyWechatPayment:`, error.response?.data || error.message);
    throw error;
  }
};

// ===== 订单相关API接口 (按照API文档标准) =====

// 创建订单
export const createOrder = async (resourceId, phone) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/orders`, {
      resourceId,
      phone
    });
    return response.data;
  } catch (error) {
    console.error(`API Error in createOrder:`, error.response?.data || error.message);
    throw error;
  }
};

// 上传支付凭证
export const uploadPaymentProof = async (orderId, paymentProof, notes) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/orders/${orderId}/payment-proof`, {
      paymentProof,
      notes
    });
    return response.data;
  } catch (error) {
    console.error(`API Error in uploadPaymentProof:`, error.response?.data || error.message);
    throw error;
  }
};

// 获取单个订单详情
export const getOrderDetails = async (orderId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/orders/${orderId}`);
    return response.data;
  } catch (error) {
    console.error(`API Error in getOrderDetails:`, error.response?.data || error.message);
    throw error;
  }
};

// 获取所有订单（管理员）
export const getAllOrders = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/orders`);
    return response.data;
  } catch (error) {
    console.error(`API Error in getAllOrders:`, error.response?.data || error.message);
    throw error;
  }
};

// 管理员审核订单
export const reviewOrder = async (orderId, action, reviewNotes) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/orders/${orderId}/review`, {
      action, // 'approve' | 'reject'
      reviewNotes
    });
    return response.data;
  } catch (error) {
    console.error(`API Error in reviewOrder:`, error.response?.data || error.message);
    throw error;
  }
};

// 验证支付并获取下载链接
export const verifyPaymentAndDownload = async (orderId) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/verify-payment/${orderId}`, {});
    return response.data;
  } catch (error) {
    console.error(`API Error in verifyPaymentAndDownload:`, error.response?.data || error.message);
    throw error;
  }
};