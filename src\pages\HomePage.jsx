// src/pages/HomePage.jsx
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getResources } from '../services/api'; // 稍后会创建这个文件

const HomePage = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCourses = async () => {
      console.log("开始获取课程数据...");

      // 先设置模拟数据，确保页面有内容显示
      const mockCourses = [
        {
          id: 1,
          title: "React 入门到精通",
          description: "从零开始学习 React，包括组件、状态管理、路由等核心概念。适合初学者和有一定基础的开发者。",
          imageUrl: "https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=React+Course",
          price: 299.99
        },
        {
          id: 2,
          title: "Vue.js 全栈开发",
          description: "学习 Vue.js 框架的完整开发流程，包括前端组件开发和后端 API 集成。",
          imageUrl: "https://via.placeholder.com/400x300/10B981/FFFFFF?text=Vue+Course",
          price: 399.99
        },
        {
          id: 3,
          title: "Node.js 后端开发",
          description: "掌握 Node.js 后端开发技术，包括 Express 框架、数据库操作和 API 设计。",
          imageUrl: "https://via.placeholder.com/400x300/F59E0B/FFFFFF?text=Node+Course",
          price: 499.99
        }
      ];

      setCourses(mockCourses);
      setLoading(false);
      console.log("模拟数据已设置:", mockCourses);

      // 然后尝试从后端获取真实数据
      try {
        console.log("尝试连接后端 API...");
        const response = await getResources();
        console.log("后端响应:", response);
        // 修复：后端直接返回数组，不是包装在data字段中
        if (response && response.length > 0) {
          setCourses(response);
          console.log("使用后端数据，共", response.length, "条课程");
        }
      } catch (err) {
        console.log("后端连接失败，使用模拟数据:", err.message);
        // 保持使用模拟数据
      }
    };

    fetchCourses();
  }, []); // 空数组表示只在组件挂载时执行一次

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <p className="text-lg text-secondary">正在加载课程...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-full">
        <p className="text-lg text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-primary mb-8 text-center">在线课程商城</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {courses.map(course => (
          <div
            key={course.id}
            className="bg-cardBackground rounded-lg shadow-lg overflow-hidden transition-transform duration-300 hover:scale-105 hover:shadow-xl cursor-pointer"
          >
            <Link to={`/course/${course.id}`} className="block w-full h-full">
              <div className="w-full h-56 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="text-4xl mb-2">📚</div>
                  <div className="text-sm font-medium">{course.title}</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-primary mb-2 truncate">{course.title}</h3>
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">{course.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-primary">¥ {course.price.toFixed(2)}</span>
                  <button className="bg-primary hover:bg-indigo-700 text-white px-4 py-2 rounded-md transition duration-200 text-sm">
                    查看详情
                  </button>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HomePage;