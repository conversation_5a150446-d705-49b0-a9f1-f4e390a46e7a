// src/App.jsx
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import HomePage from './pages/HomePage';
import CourseDetailPage from './pages/CourseDetailPage';
import LoginPage from './pages/LoginPage';
import AdminPage from './pages/AdminPage';
import PaymentAdminPage from './pages/PaymentAdminPage';
import Header from './components/Header';
import Footer from './components/Footer';
import ProtectedRoute from './ProtectedRoute'; // 导入 ProtectedRoute
import { AuthProvider } from './AuthContext'; // 导入 AuthProvider

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="flex flex-col min-h-screen bg-background text-secondary font-sans">
          <Header />
          <main className="flex-grow container mx-auto px-4 py-8">
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/course/:id" element={<CourseDetailPage />} />
              <Route path="/login" element={<LoginPage />} />

              {/* 使用 ProtectedRoute 包裹 AdminPage */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute>
                    <AdminPage />
                  </ProtectedRoute>
                }
              />

              {/* 支付审核管理页面 */}
              <Route
                path="/payment-admin"
                element={
                  <ProtectedRoute>
                    <PaymentAdminPage />
                  </ProtectedRoute>
                }
              />
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;