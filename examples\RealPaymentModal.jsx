// 真实微信支付前端组件
import React, { useState, useEffect } from 'react';
import QRCode from 'qrcode.react'; // 需要安装: npm install qrcode.react

const RealPaymentModal = ({ isOpen, onClose, course, onPaymentSuccess }) => {
  const [paymentStep, setPaymentStep] = useState('creating'); // creating, paying, checking, success, failed
  const [orderId, setOrderId] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(300); // 5分钟倒计时

  // 创建支付订单
  const createPaymentOrder = async () => {
    try {
      setPaymentStep('creating');
      setError('');

      const response = await fetch('/api/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          courseId: course.id,
          amount: course.price,
          userId: 'current_user_id' // 实际应用中从登录状态获取
        })
      });

      const result = await response.json();

      if (result.success) {
        setOrderId(result.orderId);
        setQrCodeUrl(result.qrCode);
        setPaymentStep('paying');
        startPaymentStatusCheck(result.orderId);
      } else {
        setError(result.message || '创建支付订单失败');
        setPaymentStep('failed');
      }
    } catch (err) {
      console.error('创建支付订单错误:', err);
      setError('网络错误，请稍后重试');
      setPaymentStep('failed');
    }
  };

  // 开始检查支付状态
  const startPaymentStatusCheck = (orderId) => {
    const checkInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/payment-status/${orderId}`);
        const result = await response.json();

        if (result.success && result.status === 'PAID') {
          clearInterval(checkInterval);
          setPaymentStep('success');
          setTimeout(() => {
            onPaymentSuccess(result.downloadUrl);
          }, 2000);
        }
      } catch (err) {
        console.error('检查支付状态错误:', err);
      }
    }, 2000); // 每2秒检查一次

    // 5分钟后停止检查
    setTimeout(() => {
      clearInterval(checkInterval);
      if (paymentStep === 'paying') {
        setPaymentStep('failed');
        setError('支付超时，请重新创建订单');
      }
    }, 300000);
  };

  // 倒计时
  useEffect(() => {
    if (paymentStep === 'paying' && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [paymentStep, countdown]);

  // 格式化倒计时显示
  const formatCountdown = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 当模态框打开时创建订单
  useEffect(() => {
    if (isOpen) {
      createPaymentOrder();
      setCountdown(300);
    }
  }, [isOpen]);

  const handleClose = () => {
    setPaymentStep('creating');
    setOrderId('');
    setQrCodeUrl('');
    setError('');
    setCountdown(300);
    onClose();
  };

  // 创建订单中
  const renderCreating = () => (
    <div className="text-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
      <p className="text-gray-600">正在创建支付订单...</p>
    </div>
  );

  // 等待支付
  const renderPaying = () => (
    <div className="text-center">
      <h3 className="text-xl font-semibold mb-4">微信支付</h3>
      
      <div className="mb-4">
        <p className="text-gray-600 mb-2">课程：{course?.title}</p>
        <p className="text-2xl font-bold text-blue-600">¥ {course?.price?.toFixed(2)}</p>
        <p className="text-sm text-gray-500">订单号：{orderId}</p>
      </div>

      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <p className="text-sm text-gray-600 mb-4">请使用微信扫描下方二维码完成支付</p>
        
        <div className="flex justify-center mb-4">
          <div className="bg-white p-4 rounded-lg shadow-md">
            {qrCodeUrl && (
              <QRCode 
                value={qrCodeUrl} 
                size={200}
                level="M"
                includeMargin={true}
              />
            )}
          </div>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-600 mb-2">支付剩余时间</p>
          <p className="text-lg font-mono text-red-500">{formatCountdown(countdown)}</p>
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg mb-4">
        <p className="text-sm text-blue-700">
          💡 支付完成后无需手动操作，系统会自动检测支付状态
        </p>
      </div>

      <button
        onClick={handleClose}
        className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
      >
        取消支付
      </button>
    </div>
  );

  // 支付成功
  const renderSuccess = () => (
    <div className="text-center py-8">
      <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
      <h3 className="text-xl font-semibold mb-4 text-green-600">支付成功！</h3>
      <p className="text-gray-600 mb-4">
        支付已完成，即将为您提供下载链接...
      </p>
    </div>
  );

  // 支付失败
  const renderFailed = () => (
    <div className="text-center py-8">
      <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </div>
      <h3 className="text-xl font-semibold mb-4 text-red-600">支付失败</h3>
      <p className="text-gray-600 mb-4">{error}</p>
      
      <div className="flex space-x-4">
        <button
          onClick={handleClose}
          className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          关闭
        </button>
        <button
          onClick={createPaymentOrder}
          className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          重新支付
        </button>
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 relative">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-2xl font-bold text-gray-800">微信支付</h3>
          <button onClick={handleClose} className="text-gray-500 hover:text-gray-700">
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        {paymentStep === 'creating' && renderCreating()}
        {paymentStep === 'paying' && renderPaying()}
        {paymentStep === 'success' && renderSuccess()}
        {paymentStep === 'failed' && renderFailed()}
      </div>
    </div>
  );
};

export default RealPaymentModal;
