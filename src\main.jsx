// src/main.jsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.jsx';
import './index.css';
import { AuthProvider } from './AuthContext.jsx'; // 导入 AuthProvider

// 为 Vite 的默认导入 Logo 添加占位符，如果 Vite 模板还未更新的话
// import reactLogo from './assets/react.svg';
// import viteLogo from '/vite.svg';

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <AuthProvider> {/* 用 AuthProvider 包裹整个 App */}
      <App />
    </AuthProvider>
  </React.StrictMode>,
);