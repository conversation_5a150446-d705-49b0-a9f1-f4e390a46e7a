// backend/server.js
const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');
const multer = require('multer');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 静态文件服务 - 用于访问上传的支付截图
app.use('/uploads', express.static('uploads'));

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/payment-proofs/';
    // 确保目录存在
    require('fs').mkdirSync(uploadDir, { recursive: true });
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'));
    }
  }
});

// 模拟数据存储
let courses = [
  {
    id: 1,
    title: "React 入门到精通",
    description: "从零开始学习 React，包括组件、状态管理、路由等核心概念。适合初学者和有一定基础的开发者。",
    imageUrl: "https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=React+Course",
    price: 299.99,
    baiduYunUrl: "https://pan.baidu.com/s/1example_react_course"
  },
  {
    id: 2,
    title: "Vue.js 全栈开发",
    description: "学习 Vue.js 框架的完整开发流程，包括前端组件开发和后端 API 集成。",
    imageUrl: "https://via.placeholder.com/400x300/10B981/FFFFFF?text=Vue+Course",
    price: 399.99,
    baiduYunUrl: "https://pan.baidu.com/s/1example_vue_course"
  },
  {
    id: 3,
    title: "Node.js 后端开发",
    description: "掌握 Node.js 后端开发技术，包括 Express 框架、数据库操作和 API 设计。",
    imageUrl: "https://via.placeholder.com/400x300/F59E0B/FFFFFF?text=Node+Course",
    price: 499.99,
    baiduYunUrl: "https://pan.baidu.com/s/1example_node_course"
  }
];

// 模拟微信支付交易记录（在实际应用中，这应该从微信支付API获取）
const wechatTransactions = [
  {
    transactionId: "4200002685202506224049388136",
    amount: 299.99,
    status: "SUCCESS",
    createTime: "2024-06-28T10:30:00Z"
  },
  {
    transactionId: "4200002685202506224049388137",
    amount: 399.99,
    status: "SUCCESS",
    createTime: "2024-06-28T11:00:00Z"
  },
  {
    transactionId: "4200002685202506224049388138",
    amount: 499.99,
    status: "SUCCESS",
    createTime: "2024-06-28T11:30:00Z"
  }
];

// 已使用的交易单号记录
let usedTransactions = new Set();

// 支付订单存储
let paymentOrders = [];

// 新的订单存储（按照您的API规范）
let orders = [
  {
    orderId: 1,
    resourceId: 1,
    resourceTitle: "React入门到精通",
    phone: "13800138000",
    amount: 299.99,
    status: "submitted",
    paymentProof: "payment_screenshot.jpg",
    notes: "已完成支付",
    createdAt: "2025-06-29T06:49:40.000Z",
    updatedAt: "2025-06-29T06:50:07.000Z"
  }
];

// API 路由

// 获取所有课程
app.get('/api/resources', (req, res) => {
  res.json(courses);
});

// 获取所有订单 - 按照API文档标准
app.get('/api/orders', (req, res) => {
  try {
    // 按照API文档格式返回订单列表
    const formattedOrders = orders.map(order => ({
      id: order.orderId,
      resourceId: order.resourceId,
      resourceTitle: order.courseName,
      phone: order.phone,
      amount: order.price,
      paymentMethod: 'manual',
      status: order.status,
      paymentProof: order.paymentProof || null,
      notes: order.notes || '',
      reviewNotes: order.reviewNotes || '',
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    }));

    res.json(formattedOrders);
  } catch (error) {
    console.error('获取订单列表失败:', error);
    res.status(500).json({
      message: '服务器内部错误'
    });
  }
});

// 获取单个课程
app.get('/api/resources/:id', (req, res) => {
  const courseId = parseInt(req.params.id);
  const course = courses.find(c => c.id === courseId);

  if (course) {
    res.json(course);
  } else {
    res.status(404).json({ error: '课程未找到' });
  }
});

// 添加课程
app.post('/api/resources', (req, res) => {
  const newCourse = {
    id: Math.max(...courses.map(c => c.id)) + 1,
    ...req.body,
    price: parseFloat(req.body.price) || 0 // 确保价格是数字类型
  };
  courses.push(newCourse);
  res.json({ id: newCourse.id, message: '课程添加成功' });
});

// 更新课程
app.put('/api/resources/:id', (req, res) => {
  const courseId = parseInt(req.params.id);
  const courseIndex = courses.findIndex(c => c.id === courseId);

  if (courseIndex !== -1) {
    const updateData = { ...req.body };
    if (updateData.price !== undefined) {
      updateData.price = parseFloat(updateData.price) || 0; // 确保价格是数字类型
    }
    courses[courseIndex] = { ...courses[courseIndex], ...updateData };
    res.json({ message: '资源更新成功' });
  } else {
    res.status(404).json({ error: '课程未找到' });
  }
});

// 删除课程
app.delete('/api/resources/:id', (req, res) => {
  const courseId = parseInt(req.params.id);
  const courseIndex = courses.findIndex(c => c.id === courseId);
  
  if (courseIndex !== -1) {
    courses.splice(courseIndex, 1);
    res.json({ message: '课程删除成功' });
  } else {
    res.status(404).json({ error: '课程未找到' });
  }
});

// 登录
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;
  
  // 简单的硬编码验证（在实际应用中应该使用数据库和加密）
  if (username === 'admin' && password === 'admin123') {
    res.json({ success: true, message: '登录成功' });
  } else {
    res.status(401).json({ success: false, message: '用户名或密码错误' });
  }
});

// 验证微信支付交易单号
app.post('/api/verify-wechat-payment', (req, res) => {
  const { transactionId, courseId, amount } = req.body;
  
  console.log('收到支付验证请求:', { transactionId, courseId, amount });
  
  // 检查交易单号是否已被使用
  if (usedTransactions.has(transactionId)) {
    return res.json({
      success: false,
      message: '该交易单号已被使用，请勿重复使用'
    });
  }
  
  // 查找对应的课程
  const course = courses.find(c => c.id === parseInt(courseId));
  if (!course) {
    return res.json({
      success: false,
      message: '课程不存在'
    });
  }
  
  // 验证交易单号是否存在于微信支付记录中
  const transaction = wechatTransactions.find(t => t.transactionId === transactionId);
  
  if (!transaction) {
    return res.json({
      success: false,
      message: '交易单号不存在，请检查是否输入正确'
    });
  }
  
  // 验证交易状态
  if (transaction.status !== 'SUCCESS') {
    return res.json({
      success: false,
      message: '交易未成功，请确认支付状态'
    });
  }
  
  // 验证金额是否匹配（允许小数点误差）
  if (Math.abs(transaction.amount - amount) > 0.01) {
    return res.json({
      success: false,
      message: `支付金额不匹配，期望 ¥${amount}，实际 ¥${transaction.amount}`
    });
  }
  
  // 验证成功，标记交易单号为已使用
  usedTransactions.add(transactionId);
  
  console.log('支付验证成功:', transactionId);
  
  res.json({
    success: true,
    message: '支付验证成功',
    downloadUrl: course.baiduYunUrl,
    course: {
      id: course.id,
      title: course.title
    }
  });
});

// 获取下载链接（需要验证支付）
app.get('/api/download/:resourceId', (req, res) => {
  const courseId = parseInt(req.params.resourceId);
  const course = courses.find(c => c.id === courseId);
  
  if (course) {
    res.json({
      downloadUrl: course.baiduYunUrl,
      course: {
        id: course.id,
        title: course.title
      }
    });
  } else {
    res.status(404).json({ error: '课程未找到' });
  }
});

// 提交支付凭证
app.post('/api/submit-payment-proof', upload.single('paymentProof'), async (req, res) => {
  console.log('收到支付凭证提交请求');
  console.log('请求体:', req.body);
  console.log('上传文件:', req.file);

  try {
    const { orderId, courseId, userInfo } = req.body;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传支付截图'
      });
    }

    if (!orderId || !courseId || !userInfo) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    const userInfoParsed = JSON.parse(userInfo);

    const order = {
      orderId,
      courseId: parseInt(courseId),
      userInfo: userInfoParsed,
      proofImage: req.file.filename,
      status: 'PENDING', // PENDING, APPROVED, REJECTED
      submitTime: new Date(),
      reviewTime: null,
      reviewer: null,
      reviewNote: ''
    };

    paymentOrders.push(order);

    res.json({
      success: true,
      message: '支付凭证已提交，请等待审核'
    });

  } catch (error) {
    console.error('提交支付凭证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 查询支付状态
app.get('/api/payment-status/:orderId', (req, res) => {
  const { orderId } = req.params;
  const order = paymentOrders.find(o => o.orderId === orderId);

  if (!order) {
    return res.status(404).json({
      success: false,
      message: '订单不存在'
    });
  }

  const response = {
    orderId: order.orderId,
    status: order.status,
    submitTime: order.submitTime,
    reviewTime: order.reviewTime
  };

  if (order.status === 'APPROVED') {
    // 根据课程ID获取下载链接
    const course = courses.find(c => c.id === order.courseId);
    response.downloadUrl = course?.baiduYunUrl;
  } else if (order.status === 'REJECTED') {
    response.reason = order.reviewNote;
  }

  res.json(response);
});

// 管理员获取待审核订单列表
app.get('/api/admin/pending-orders', (req, res) => {
  console.log('orders 数组:', orders);
  console.log('paymentOrders 数组:', paymentOrders);
  console.log('待审核订单筛选条件: status === "submitted" 或 "PENDING"');

  // 合并两个数组的数据，优先使用 orders 数组
  const allOrders = [...orders];

  // 如果 orders 数组为空，使用 paymentOrders 数组
  if (allOrders.length === 0) {
    paymentOrders.forEach(order => {
      allOrders.push({
        orderId: order.orderId,
        resourceTitle: order.courseName,
        amount: order.userInfo?.amount || order.price,
        paymentProof: order.proofImage,
        notes: order.userInfo?.remark || '',
        createdAt: order.submitTime,
        updatedAt: order.submitTime,
        status: order.status === 'PENDING' ? 'submitted' : order.status.toLowerCase()
      });
    });
  }

  const pendingOrders = allOrders
    .filter(order => order.status === 'submitted' || order.status === 'PENDING')
    .sort((a, b) => new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt));

  console.log('筛选出的待审核订单:', pendingOrders);

  res.json({
    success: true,
    orders: pendingOrders.map(order => ({
      orderId: order.orderId,
      resourceTitle: order.resourceTitle || order.courseName,
      amount: order.amount || order.price,
      paymentProof: order.paymentProof,
      notes: order.notes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      status: order.status
    }))
  });
});

// 管理员审核订单
app.post('/api/admin/review-order', (req, res) => {
  const { orderId, action, note, reviewer } = req.body; // action: 'approve' | 'reject'

  const orderIndex = orders.findIndex(o => o.orderId == orderId);

  if (orderIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '订单不存在'
    });
  }

  const order = orders[orderIndex];

  if (order.status !== 'submitted') {
    return res.status(400).json({
      success: false,
      message: '订单已经被审核过了'
    });
  }

  // 更新订单状态
  orders[orderIndex] = {
    ...order,
    status: action === 'approve' ? 'approved' : 'rejected',
    reviewTime: new Date().toISOString(),
    reviewer: reviewer || 'admin',
    reviewNote: note || ''
  };

  res.json({
    success: true,
    message: `订单已${action === 'approve' ? '通过' : '拒绝'}审核`
  });
});

// 管理员统计数据
app.get('/api/admin/stats', (req, res) => {
  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'submitted').length,
    approved: orders.filter(o => o.status === 'approved').length,
    rejected: orders.filter(o => o.status === 'rejected').length,
    todayOrders: orders.filter(o => {
      const today = new Date();
      const orderDate = new Date(o.createdAt);
      return orderDate.toDateString() === today.toDateString();
    }).length,
    totalRevenue: orders
      .filter(o => o.status === 'approved')
      .reduce((sum, o) => sum + parseFloat(o.amount), 0)
  };

  res.json({
    success: true,
    stats
  });
});

// 创建订单接口 - 按照API文档标准
app.post('/api/orders', (req, res) => {
  try {
    const { resourceId, phone } = req.body;

    if (!resourceId || !phone) {
      return res.status(400).json({
        message: '缺少必要参数: resourceId 和 phone'
      });
    }

    // 根据resourceId查找课程信息
    const course = courses.find(c => c.id === parseInt(resourceId));
    if (!course) {
      return res.status(400).json({
        message: '资源不存在'
      });
    }

    // 生成数字订单ID
    const orderId = Math.max(...orders.map(o => o.orderId || 0), 0) + 1;

    const order = {
      orderId,
      resourceId: parseInt(resourceId),
      courseName: course.title,
      price: course.price,
      phone,
      status: 'pending',
      createdAt: new Date().toISOString(),
      paymentProof: null,
      userInfo: null
    };

    orders.push(order);

    // 按照API文档格式返回响应
    res.json({
      message: '订单创建成功',
      orderId: orderId,
      amount: course.price,
      resourceTitle: course.title
    });
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({
      message: '服务器内部错误'
    });
  }
});

// 上传支付凭证接口 - 按照API文档标准
app.post('/api/orders/:orderId/payment-proof', (req, res) => {
  try {
    const { orderId } = req.params;
    const { paymentProof, notes } = req.body;

    if (!paymentProof) {
      return res.status(400).json({
        message: '支付凭证是必填项'
      });
    }

    const order = orders.find(o => o.orderId == orderId);
    if (!order) {
      return res.status(404).json({
        message: '订单不存在'
      });
    }

    // 更新订单信息
    order.paymentProof = paymentProof;
    order.notes = notes || '';
    order.status = 'submitted';
    order.updatedAt = new Date().toISOString();

    res.json({
      message: '支付凭证上传成功，等待审核',
      orderId: parseInt(orderId),
      status: 'submitted'
    });
  } catch (error) {
    console.error('上传支付凭证失败:', error);
    res.status(500).json({
      message: '服务器内部错误'
    });
  }
});

// 获取单个订单详情接口 - 按照API文档标准
app.get('/api/orders/:orderId', (req, res) => {
  try {
    const { orderId } = req.params;

    const order = orders.find(o => o.orderId == orderId);
    if (!order) {
      return res.status(404).json({
        message: '订单不存在'
      });
    }

    // 按照API文档格式返回订单详情
    const response = {
      id: order.orderId,
      resourceId: order.resourceId,
      resourceTitle: order.courseName,
      phone: order.phone,
      amount: order.price,
      paymentMethod: 'manual',
      status: order.status,
      paymentProof: order.paymentProof || null,
      notes: order.notes || '',
      reviewNotes: order.reviewNotes || '',
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    };

    res.json(response);
  } catch (error) {
    console.error('获取订单详情失败:', error);
    res.status(500).json({
      message: '服务器内部错误'
    });
  }
});

// 管理员获取待审核订单接口
app.get('/api/admin/orders', (req, res) => {
  try {
    const pendingOrders = orders.filter(o => o.status === 'submitted').map(order => ({
      orderId: order.orderId,
      courseName: order.courseName,
      price: order.price,
      userInfo: order.userInfo,
      createdAt: order.createdAt,
      status: order.status
    }));

    res.json({
      success: true,
      data: pendingOrders
    });
  } catch (error) {
    console.error('获取待审核订单失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 管理员审核订单接口 - 按照API文档标准
app.post('/api/orders/:orderId/review', (req, res) => {
  try {
    const { orderId } = req.params;
    const { action, reviewNotes } = req.body; // action: 'approve' | 'reject'

    const order = orders.find(o => o.orderId == orderId);
    if (!order) {
      return res.status(404).json({
        message: '订单不存在'
      });
    }

    if (action === 'approve') {
      order.status = 'approved';
    } else if (action === 'reject') {
      order.status = 'rejected';
    } else {
      return res.status(400).json({
        message: '无效的操作'
      });
    }

    order.reviewNotes = reviewNotes || '';
    order.updatedAt = new Date().toISOString();

    res.json({
      message: `订单审核${action === 'approve' ? '通过' : '拒绝'}`,
      orderId: parseInt(orderId),
      status: order.status
    });
  } catch (error) {
    console.error('审核订单失败:', error);
    res.status(500).json({
      message: '服务器内部错误'
    });
  }
});

// 验证支付并获取下载链接接口 - 按照API文档标准
app.post('/api/verify-payment/:orderId', (req, res) => {
  try {
    const { orderId } = req.params;

    const order = orders.find(o => o.orderId == orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    if (order.status !== 'approved') {
      return res.status(400).json({
        success: false,
        message: '订单尚未审核通过，无法获取下载链接',
        status: order.status
      });
    }

    // 根据resourceId获取课程信息
    const course = courses.find(c => c.id === order.resourceId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    res.json({
      success: true,
      message: '支付验证成功，为您提供下载链接！',
      downloadUrl: course.baiduYunUrl,
      orderId: parseInt(orderId),
      resourceId: order.resourceId,
      resourceTitle: order.courseName
    });
  } catch (error) {
    console.error('验证支付失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log('API 端点:');
  console.log('  GET  /api/resources - 获取所有课程');
  console.log('  GET  /api/resources/:id - 获取单个课程');
  console.log('  POST /api/resources - 添加课程');
  console.log('  PUT  /api/resources/:id - 更新课程');
  console.log('  DELETE /api/resources/:id - 删除课程');
  console.log('  POST /api/login - 登录');
  console.log('  POST /api/orders - 创建订单');
  console.log('  POST /api/payment-proof - 上传支付凭证');
  console.log('  GET  /api/orders/:orderId/status - 查询订单状态');
  console.log('  GET  /api/admin/orders - 获取待审核订单');
  console.log('  POST /api/admin/orders/:orderId/review - 审核订单');
  console.log('  POST /api/verify-wechat-payment - 验证微信支付');
  console.log('  GET  /api/download/:resourceId - 获取下载链接');
  console.log('  POST /api/submit-payment-proof - 提交支付凭证');
  console.log('  GET  /api/payment-status/:orderId - 查询支付状态');
  console.log('  GET  /api/admin/pending-orders - 获取待审核订单');
  console.log('  POST /api/admin/review-order - 审核订单');
  console.log('  GET  /api/admin/stats - 获取统计数据');
  console.log('  GET  /api/health - 健康检查');
  console.log('\n模拟的微信交易单号:');
  wechatTransactions.forEach(t => {
    console.log(`  ${t.transactionId} - ¥${t.amount}`);
  });
});
