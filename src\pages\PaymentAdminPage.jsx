// 支付审核管理页面
import React, { useState, useEffect } from 'react';

const PaymentAdminPage = () => {
  const [pendingOrders, setPendingOrders] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [reviewNote, setReviewNote] = useState('');

  // 获取待审核订单
  const fetchPendingOrders = async () => {
    try {
      console.log('正在获取待审核订单...');
      const response = await fetch('/api/admin/pending-orders');
      console.log('API响应状态:', response.status);
      console.log('API响应URL:', response.url);
      const result = await response.json();
      console.log('API响应数据:', result);
      if (result.success) {
        console.log('设置待审核订单:', result.orders);
        setPendingOrders(result.orders);
      } else {
        console.error('API返回失败:', result);
      }
    } catch (error) {
      console.error('获取待审核订单失败:', error);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats');
      const result = await response.json();
      if (result.success) {
        setStats(result.stats);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  // 审核订单
  const reviewOrder = async (orderId, action) => {
    try {
      const response = await fetch('/api/admin/review-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          action,
          note: reviewNote,
          reviewer: 'admin'
        })
      });

      const result = await response.json();
      if (result.success) {
        alert(result.message);
        setSelectedOrder(null);
        setReviewNote('');
        fetchPendingOrders();
        fetchStats();
      } else {
        alert(result.message);
      }
    } catch (error) {
      console.error('审核订单失败:', error);
      alert('审核失败，请重试');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchPendingOrders(), fetchStats()]);
      setLoading(false);
    };
    
    loadData();
    
    // 每30秒刷新一次
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <p className="text-lg text-gray-600">正在加载...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">支付审核管理</h1>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">待审核</h3>
          <p className="text-3xl font-bold text-orange-600">{stats.pending || 0}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">已通过</h3>
          <p className="text-3xl font-bold text-green-600">{stats.approved || 0}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">今日订单</h3>
          <p className="text-3xl font-bold text-blue-600">{stats.todayOrders || 0}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">总收入</h3>
          <p className="text-3xl font-bold text-purple-600">¥{(stats.totalRevenue || 0).toFixed(2)}</p>
        </div>
      </div>

      {/* 待审核订单列表 */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">待审核订单</h2>
        </div>
        
        {pendingOrders.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            暂无待审核订单
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    订单信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    用户信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    支付信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    提交时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {pendingOrders.map((order) => (
                  <tr key={order.orderId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {order.orderId}
                        </div>
                        <div className="text-sm text-gray-500">
                          课程ID: {order.courseId}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {order.userInfo.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {order.userInfo.email}
                        </div>
                        <div className="text-sm text-gray-500">
                          {order.userInfo.phone}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          ¥{order.userInfo.amount}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(order.userInfo.paymentTime).toLocaleString()}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(order.submitTime).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => setSelectedOrder(order)}
                        className="text-blue-600 hover:text-blue-900 mr-4"
                      >
                        查看详情
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 订单详情模态框 */}
      {selectedOrder && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl p-6 max-h-screen overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-gray-800">订单详情</h3>
              <button
                onClick={() => setSelectedOrder(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              {/* 订单基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">订单号</label>
                  <p className="text-sm text-gray-900">{selectedOrder.orderId}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">课程ID</label>
                  <p className="text-sm text-gray-900">{selectedOrder.courseId}</p>
                </div>
              </div>

              {/* 用户信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">用户姓名</label>
                  <p className="text-sm text-gray-900">{selectedOrder.userInfo.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">邮箱</label>
                  <p className="text-sm text-gray-900">{selectedOrder.userInfo.email}</p>
                </div>
              </div>

              {/* 支付信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">支付金额</label>
                  <p className="text-sm text-gray-900">¥{selectedOrder.userInfo.amount}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">支付时间</label>
                  <p className="text-sm text-gray-900">
                    {new Date(selectedOrder.userInfo.paymentTime).toLocaleString()}
                  </p>
                </div>
              </div>

              {/* 支付截图 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">支付截图</label>
                <img
                  src={selectedOrder.proofImageUrl}
                  alt="支付截图"
                  className="max-w-full h-auto border border-gray-300 rounded-lg"
                />
              </div>

              {/* 备注 */}
              {selectedOrder.userInfo.remark && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">用户备注</label>
                  <p className="text-sm text-gray-900">{selectedOrder.userInfo.remark}</p>
                </div>
              )}

              {/* 审核备注 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">审核备注</label>
                <textarea
                  value={reviewNote}
                  onChange={(e) => setReviewNote(e.target.value)}
                  placeholder="请填写审核备注（可选）"
                  rows="3"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-4 pt-4">
                <button
                  onClick={() => reviewOrder(selectedOrder.orderId, 'reject')}
                  className="flex-1 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                >
                  拒绝
                </button>
                <button
                  onClick={() => reviewOrder(selectedOrder.orderId, 'approve')}
                  className="flex-1 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                >
                  通过
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentAdminPage;
