// 真实微信支付后端实现示例
const express = require('express');
const crypto = require('crypto');
const axios = require('axios');
const xml2js = require('xml2js');

const app = express();

// 微信支付配置
const WECHAT_CONFIG = {
  appId: 'your_app_id',           // 微信公众号/小程序AppID
  mchId: 'your_merchant_id',      // 商户号
  apiKey: 'your_api_key',         // API密钥
  notifyUrl: 'https://yourdomain.com/api/wechat/notify'  // 支付回调地址
};

// 生成随机字符串
function generateNonceStr() {
  return Math.random().toString(36).substr(2, 15);
}

// 生成签名
function generateSign(params, apiKey) {
  const sortedKeys = Object.keys(params).sort();
  const stringA = sortedKeys.map(key => `${key}=${params[key]}`).join('&');
  const stringSignTemp = `${stringA}&key=${apiKey}`;
  return crypto.createHash('md5').update(stringSignTemp).digest('hex').toUpperCase();
}

// 对象转XML
function buildXML(obj) {
  const builder = new xml2js.Builder({ rootName: 'xml', headless: true });
  return builder.buildObject(obj);
}

// XML转对象
function parseXML(xml) {
  return new Promise((resolve, reject) => {
    xml2js.parseString(xml, { explicitArray: false }, (err, result) => {
      if (err) reject(err);
      else resolve(result.xml);
    });
  });
}

// 创建支付订单
app.post('/api/create-payment', async (req, res) => {
  try {
    const { courseId, amount, userId } = req.body;
    
    // 1. 生成订单号
    const outTradeNo = `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 2. 准备微信支付参数
    const params = {
      appid: WECHAT_CONFIG.appId,
      mch_id: WECHAT_CONFIG.mchId,
      nonce_str: generateNonceStr(),
      body: `课程购买-${courseId}`,
      out_trade_no: outTradeNo,
      total_fee: Math.round(amount * 100), // 微信支付金额单位为分
      spbill_create_ip: req.ip,
      notify_url: WECHAT_CONFIG.notifyUrl,
      trade_type: 'NATIVE' // 扫码支付
    };
    
    // 3. 生成签名
    params.sign = generateSign(params, WECHAT_CONFIG.apiKey);
    
    // 4. 调用微信统一下单API
    const xmlData = buildXML(params);
    const response = await axios.post('https://api.mch.weixin.qq.com/pay/unifiedorder', xmlData, {
      headers: { 'Content-Type': 'application/xml' }
    });
    
    // 5. 解析微信返回结果
    const result = await parseXML(response.data);
    
    if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
      // 6. 保存订单到数据库
      const order = {
        orderId: outTradeNo,
        courseId,
        userId,
        amount,
        status: 'PENDING',
        wechatOrderId: result.prepay_id,
        qrCode: result.code_url,
        createTime: new Date()
      };
      
      // 这里应该保存到数据库
      // await saveOrderToDatabase(order);
      
      res.json({
        success: true,
        orderId: outTradeNo,
        qrCode: result.code_url, // 这个就是支付二维码
        amount: amount
      });
    } else {
      res.json({
        success: false,
        message: result.err_code_des || '创建支付订单失败'
      });
    }
  } catch (error) {
    console.error('创建支付订单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 微信支付回调处理
app.post('/api/wechat/notify', async (req, res) => {
  try {
    // 1. 解析微信回调数据
    const result = await parseXML(req.body);
    
    // 2. 验证签名
    const sign = result.sign;
    delete result.sign;
    const calculatedSign = generateSign(result, WECHAT_CONFIG.apiKey);
    
    if (sign !== calculatedSign) {
      console.error('微信回调签名验证失败');
      return res.send('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[签名失败]]></return_msg></xml>');
    }
    
    // 3. 检查支付结果
    if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
      const orderId = result.out_trade_no;
      const transactionId = result.transaction_id;
      const totalFee = parseInt(result.total_fee) / 100; // 转换为元
      
      // 4. 更新订单状态
      // await updateOrderStatus(orderId, 'PAID', transactionId);
      
      // 5. 这里可以发送邮件、短信通知用户
      // await notifyUser(orderId);
      
      console.log(`订单 ${orderId} 支付成功，微信交易号：${transactionId}`);
      
      // 6. 返回成功响应给微信
      res.send('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>');
    } else {
      console.error('微信支付失败:', result);
      res.send('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[支付失败]]></return_msg></xml>');
    }
  } catch (error) {
    console.error('处理微信回调错误:', error);
    res.send('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统错误]]></return_msg></xml>');
  }
});

// 查询支付状态
app.get('/api/payment-status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    // 1. 准备查询参数
    const params = {
      appid: WECHAT_CONFIG.appId,
      mch_id: WECHAT_CONFIG.mchId,
      out_trade_no: orderId,
      nonce_str: generateNonceStr()
    };
    
    // 2. 生成签名
    params.sign = generateSign(params, WECHAT_CONFIG.apiKey);
    
    // 3. 调用微信查询API
    const xmlData = buildXML(params);
    const response = await axios.post('https://api.mch.weixin.qq.com/pay/orderquery', xmlData, {
      headers: { 'Content-Type': 'application/xml' }
    });
    
    // 4. 解析结果
    const result = await parseXML(response.data);
    
    if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
      const paymentStatus = result.trade_state;
      
      if (paymentStatus === 'SUCCESS') {
        // 支付成功，更新订单状态
        // await updateOrderStatus(orderId, 'PAID', result.transaction_id);
        
        res.json({
          success: true,
          status: 'PAID',
          transactionId: result.transaction_id,
          downloadUrl: 'https://pan.baidu.com/s/1example' // 返回下载链接
        });
      } else {
        res.json({
          success: true,
          status: paymentStatus // NOTPAY, CLOSED, REVOKED, USERPAYING, PAYERROR
        });
      }
    } else {
      res.json({
        success: false,
        message: '查询支付状态失败'
      });
    }
  } catch (error) {
    console.error('查询支付状态错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

module.exports = app;
