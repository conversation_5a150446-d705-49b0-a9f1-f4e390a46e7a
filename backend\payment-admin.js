// 个人收款审核管理系统
const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/payment-proofs/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'));
    }
  }
});

// 模拟数据库存储
let paymentOrders = [];

// 提交支付凭证
router.post('/submit-payment-proof', upload.single('paymentProof'), async (req, res) => {
  try {
    const { orderId, courseId, userInfo } = req.body;
    const userInfoParsed = JSON.parse(userInfo);
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传支付截图'
      });
    }

    const order = {
      orderId,
      courseId: parseInt(courseId),
      userInfo: userInfoParsed,
      proofImage: req.file.filename,
      status: 'PENDING', // PENDING, APPROVED, REJECTED
      submitTime: new Date(),
      reviewTime: null,
      reviewer: null,
      reviewNote: ''
    };

    paymentOrders.push(order);

    // 发送邮件通知管理员（可选）
    // await sendAdminNotification(order);

    res.json({
      success: true,
      message: '支付凭证已提交，请等待审核'
    });

  } catch (error) {
    console.error('提交支付凭证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 查询支付状态
router.get('/payment-status/:orderId', (req, res) => {
  const { orderId } = req.params;
  const order = paymentOrders.find(o => o.orderId === orderId);

  if (!order) {
    return res.status(404).json({
      success: false,
      message: '订单不存在'
    });
  }

  const response = {
    orderId: order.orderId,
    status: order.status,
    submitTime: order.submitTime,
    reviewTime: order.reviewTime
  };

  if (order.status === 'APPROVED') {
    // 根据课程ID获取下载链接
    const course = courses.find(c => c.id === order.courseId);
    response.downloadUrl = course?.baiduYunUrl;
  } else if (order.status === 'REJECTED') {
    response.reason = order.reviewNote;
  }

  res.json(response);
});

// 管理员获取待审核订单列表
router.get('/admin/pending-orders', (req, res) => {
  const pendingOrders = paymentOrders
    .filter(order => order.status === 'PENDING')
    .sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime));

  res.json({
    success: true,
    orders: pendingOrders.map(order => ({
      ...order,
      proofImageUrl: `/uploads/payment-proofs/${order.proofImage}`
    }))
  });
});

// 管理员审核订单
router.post('/admin/review-order', (req, res) => {
  const { orderId, action, note, reviewer } = req.body; // action: 'approve' | 'reject'
  
  const orderIndex = paymentOrders.findIndex(o => o.orderId === orderId);
  
  if (orderIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '订单不存在'
    });
  }

  const order = paymentOrders[orderIndex];
  
  if (order.status !== 'PENDING') {
    return res.status(400).json({
      success: false,
      message: '订单已经被审核过了'
    });
  }

  // 更新订单状态
  paymentOrders[orderIndex] = {
    ...order,
    status: action === 'approve' ? 'APPROVED' : 'REJECTED',
    reviewTime: new Date(),
    reviewer: reviewer || 'admin',
    reviewNote: note || ''
  };

  // 如果审核通过，发送邮件给用户（可选）
  if (action === 'approve') {
    // await sendDownloadLinkEmail(order);
  }

  res.json({
    success: true,
    message: `订单已${action === 'approve' ? '通过' : '拒绝'}审核`
  });
});

// 管理员获取所有订单
router.get('/admin/all-orders', (req, res) => {
  const { status, page = 1, limit = 20 } = req.query;
  
  let filteredOrders = paymentOrders;
  
  if (status) {
    filteredOrders = paymentOrders.filter(order => order.status === status);
  }
  
  // 分页
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedOrders = filteredOrders
    .sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime))
    .slice(startIndex, endIndex);

  res.json({
    success: true,
    orders: paginatedOrders.map(order => ({
      ...order,
      proofImageUrl: `/uploads/payment-proofs/${order.proofImage}`
    })),
    total: filteredOrders.length,
    page: parseInt(page),
    totalPages: Math.ceil(filteredOrders.length / limit)
  });
});

// 统计数据
router.get('/admin/stats', (req, res) => {
  const stats = {
    total: paymentOrders.length,
    pending: paymentOrders.filter(o => o.status === 'PENDING').length,
    approved: paymentOrders.filter(o => o.status === 'APPROVED').length,
    rejected: paymentOrders.filter(o => o.status === 'REJECTED').length,
    todayOrders: paymentOrders.filter(o => {
      const today = new Date();
      const orderDate = new Date(o.submitTime);
      return orderDate.toDateString() === today.toDateString();
    }).length,
    totalRevenue: paymentOrders
      .filter(o => o.status === 'APPROVED')
      .reduce((sum, o) => sum + parseFloat(o.userInfo.amount), 0)
  };

  res.json({
    success: true,
    stats
  });
});

// 批量处理订单（根据微信账单）
router.post('/admin/batch-process', (req, res) => {
  const { wechatBill } = req.body; // 微信账单数据
  
  // 这里可以实现自动匹配逻辑
  // 根据金额、时间、备注等信息自动匹配订单
  
  let processedCount = 0;
  
  wechatBill.forEach(billItem => {
    const matchingOrders = paymentOrders.filter(order => 
      order.status === 'PENDING' &&
      Math.abs(parseFloat(order.userInfo.amount) - billItem.amount) < 0.01 &&
      isTimeMatch(order.userInfo.paymentTime, billItem.time)
    );
    
    matchingOrders.forEach(order => {
      const orderIndex = paymentOrders.findIndex(o => o.orderId === order.orderId);
      if (orderIndex !== -1) {
        paymentOrders[orderIndex].status = 'APPROVED';
        paymentOrders[orderIndex].reviewTime = new Date();
        paymentOrders[orderIndex].reviewer = 'auto-batch';
        paymentOrders[orderIndex].reviewNote = '批量自动审核通过';
        processedCount++;
      }
    });
  });

  res.json({
    success: true,
    message: `批量处理完成，共处理 ${processedCount} 个订单`
  });
});

// 时间匹配辅助函数
function isTimeMatch(orderTime, billTime) {
  const orderDate = new Date(orderTime);
  const billDate = new Date(billTime);
  const timeDiff = Math.abs(orderDate.getTime() - billDate.getTime());
  return timeDiff < 30 * 60 * 1000; // 30分钟内算匹配
}

module.exports = router;
