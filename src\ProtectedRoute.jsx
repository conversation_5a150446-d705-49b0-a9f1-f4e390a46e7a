// src/ProtectedRoute.jsx
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from './AuthContext.jsx'; // 引入 useAuth hook

const ProtectedRoute = ({ children }) => {
  const { isAuthenticated } = useAuth(); // 从 context 获取认证状态
  const location = useLocation(); // 正确获取当前位置

  if (!isAuthenticated) {
    // 如果未登录，重定向到登录页面，并传递一个 'from' 状态，以便登录后能返回原页面
    return <Navigate to="/login" replace state={{ from: location.pathname }} />;
  }

  // 如果已登录，渲染子组件
  return children;
};

export default ProtectedRoute;