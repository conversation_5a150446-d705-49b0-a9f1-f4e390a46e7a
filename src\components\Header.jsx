// src/components/Header.jsx
import { Link } from 'react-router-dom';

const Header = () => {
  return (
    <header className="bg-white text-secondary shadow-md">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link to="/" className="text-2xl font-bold text-primary hover:text-primary-darker flex items-center">
          {/* 示例 Logo */}
          <svg className="h-8 w-8 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
          <span>知识库</span>
        </Link>
        <nav className="space-x-4">
          <Link to="/" className="hover:text-primary transition duration-200">首页</Link>
          <Link to="/courses" className="hover:text-primary transition duration-200">所有课程</Link>
          <Link to="/about" className="hover:text-primary transition duration-200">关于我</Link>
          <Link to="/payment-admin" className="hover:text-primary transition duration-200">支付管理</Link>
          <Link to="/login" className="bg-primary hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition duration-200">登录</Link>
        </nav>
      </div>
    </header>
  );
};

export default Header;