// src/pages/CourseDetailPage.jsx
import { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { getCourseById } from '../services/api';

const CourseDetailPage = () => {
  const { id } = useParams();
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCourse = async () => {
      console.log("开始获取课程详情，ID:", id);
      
      if (!id) {
        setError('课程ID无效');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const courseData = await getCourseById(id);
        console.log("获取到的课程数据:", courseData);
        
        if (courseData) {
          setCourse(courseData);
          setError(null);
        } else {
          setError('未找到该课程');
        }
      } catch (err) {
        console.error("获取课程详情失败:", err);
        setError(`加载失败: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [id]);

  const handlePurchase = () => {
    if (!course) return;
    
    const confirmed = window.confirm(`确认购买《${course.title}》吗？\n价格：¥${course.price}`);
    if (confirmed) {
      alert(`购买成功！\n课程：${course.title}\n下载链接：${course.baiduYunUrl || '暂无下载链接'}`);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <p className="text-lg text-gray-600">正在加载课程详情...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-lg text-red-500 mb-4">{error}</p>
          <Link 
            to="/" 
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md transition duration-200"
          >
            返回首页
          </Link>
        </div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-4">未找到该课程</p>
          <Link 
            to="/" 
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md transition duration-200"
          >
            返回首页
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 返回按钮 */}
      <div className="mb-6">
        <Link 
          to="/" 
          className="inline-flex items-center text-blue-500 hover:text-blue-600 transition duration-200"
        >
          ← 返回课程列表
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="md:flex">
          {/* 课程图片/图标 */}
          <div className="md:w-1/2">
            <div className="h-64 md:h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
              <div className="text-white text-center">
                <div className="text-6xl mb-4">📚</div>
                <div className="text-xl font-medium">{course.title}</div>
              </div>
            </div>
          </div>

          {/* 课程信息 */}
          <div className="md:w-1/2 p-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">{course.title}</h1>
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-700 mb-2">课程描述</h3>
              <p className="text-gray-600 leading-relaxed">{course.description}</p>
            </div>

            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-2xl font-bold text-blue-600">¥ {course.price.toFixed(2)}</span>
                <span className="text-sm text-gray-500">一次购买，永久使用</span>
              </div>
            </div>

            <div className="space-y-4">
              <button
                onClick={handlePurchase}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-md transition duration-200"
              >
                立即购买
              </button>
              
              <div className="text-sm text-gray-500 text-center">
                <p>✓ 支持在线学习</p>
                <p>✓ 提供课程资料下载</p>
                <p>✓ 永久访问权限</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetailPage;
