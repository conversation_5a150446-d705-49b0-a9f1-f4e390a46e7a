// src/pages/AdminPage.jsx (部分修改，需要替换原有内容)
import React, { useEffect, useState, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { getResources, addResource, updateResource, deleteResource } from '../services/api';
import { useAuth } from '../AuthContext.jsx'; // 引入 useAuth
import Modal from '../components/Modal'; // 稍后创建 Modal 组件

const AdminPage = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const { logout, isAdmin } = useAuth(); // 获取登出函数和管理员状态

  // --- 状态和函数用于课程管理模态框 ---
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' or 'edit'
  const [currentCourse, setCurrentCourse] = useState({
    id: null,
    title: '',
    description: '',
    imageUrl: '',
    price: 0,
    baiduYunUrl: ''
  });
  const [modalError, setModalError] = useState('');

  // --- 登录保护检查 ---
  useEffect(() => {
    // 改进的登录检查逻辑
    if (!isAdmin) { // 如果没有管理员权限，跳转到登录页
      console.log("Not authenticated or not admin, redirecting to login.");
      navigate('/login');
    }
  }, [isAdmin, navigate]);

  // --- 获取课程列表 ---
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        const response = await getResources();
        setCourses(response.data || []);
      } catch (err) {
        console.error("Error fetching courses for admin:", err);
        setError("无法加载课程列表，请联系管理员。");
      } finally {
        setLoading(false);
      }
    };
    fetchCourses();
  }, []);

  // --- 处理模态框打开 ---
  const openModal = (mode, course = null) => {
    setModalMode(mode);
    if (mode === 'edit' && course) {
      setCurrentCourse({ ...course }); // 预填编辑课程的数据
    } else {
      setCurrentCourse({ id: null, title: '', description: '', imageUrl: '', price: 0, baiduYunUrl: '' }); // 重置为添加模式
    }
    setModalError(''); // 清空模态框错误信息
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentCourse({ id: null, title: '', description: '', imageUrl: '', price: 0, baiduYunUrl: '' });
  };

  // --- 处理表单输入 ---
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentCourse(prev => ({ ...prev, [name]: value }));
  };

  // --- 处理保存课程 (添加/编辑) ---
  const handleSaveCourse = async () => {
    if (!currentCourse.title || currentCourse.price === 0) {
      setModalError('标题和价格是必填项！');
      return;
    }

    setModalError('');
    try {
      let response;
      if (modalMode === 'add') {
        // 添加课程
        const { id, ...courseData } = currentCourse; // 移除 id for add
        response = await addResource(courseData);
        if (response.id) {
          setCourses(prev => [...prev, { ...currentCourse, id: response.id }]);
        }
      } else {
        // 编辑课程
        response = await updateResource(currentCourse.id, currentCourse);
        if (response.message === '资源更新成功') { // 假设更新成功时返回此消息
          setCourses(prev => prev.map(c => c.id === currentCourse.id ? { ...currentCourse } : c));
        }
      }
      closeModal();
    } catch (err) {
      console.error(`Error ${modalMode} course:`, err);
      setModalError(`保存课程失败，请稍后重试。`);
    }
  };

  // --- 处理删除课程 ---
  const handleDeleteCourse = async (courseId) => {
    if (!window.confirm('确定要删除此课程吗？此操作不可逆！')) {
      return;
    }
    try {
      const response = await deleteResource(courseId);
      if (response.message === '资源删除成功') { // 假设删除成功时返回此消息
        setCourses(prev => prev.filter(c => c.id !== courseId));
        alert('课程删除成功！');
      }
    } catch (err) {
      console.error("Error deleting course:", err);
      alert('删除课程失败，请稍后重试。');
    }
  };

  // --- 登出 ---
  const handleLogout = () => {
    logout(); // 调用 context 的 logout
    navigate('/login'); // 跳转到登录页
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-4xl font-bold text-primary">后台管理系统</h1>
        <div>
          <button
            className="bg-primary hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200 ease-in-out mr-4"
            onClick={() => openModal('add')}
          >
            + 添加新课程
          </button>
          <button
            onClick={handleLogout}
            className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md focus:outline-none focus:shadow-outline transition duration-200 ease-in-out"
          >
            登出
          </button>
        </div>
      </div>

      {/* 课程列表表格 */}
      {loading && <p className="text-lg text-secondary">正在加载课程列表...</p>}
      {error && <p className="text-lg text-red-500">{error}</p>}

      {!loading && !error && courses.length === 0 && (
        <p className="text-lg text-secondary text-center py-12">暂无已发布的课程。</p>
      )}

      {!loading && !error && courses.length > 0 && (
        <div className="overflow-x-auto shadow-md rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  标题
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  价格
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {courses.map(course => (
                <tr key={course.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{course.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary">{course.title}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-primary font-bold">¥ {course.price.toFixed(2)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      className="text-indigo-600 hover:text-indigo-900"
                      onClick={() => openModal('edit', course)}
                    >
                      编辑
                    </button>
                    <button
                      className="text-red-600 hover:text-red-900"
                      onClick={() => handleDeleteCourse(course.id)}
                    >
                      删除
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* 添加/编辑课程模态框 (需要实现 Modal 组件) */}
      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalMode === 'add' ? '添加新课程' : '编辑课程'}
        error={modalError}
      >
        <form onSubmit={(e) => { e.preventDefault(); handleSaveCourse(); }}>
          <div className="mb-4">
            <label htmlFor="title" className="block text-sm font-semibold text-secondary mb-1">标题</label>
            <input
              type="text"
              id="title"
              name="title"
              value={currentCourse.title}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-semibold text-secondary mb-1">描述</label>
            <textarea
              id="description"
              name="description"
              value={currentCourse.description}
              onChange={handleInputChange}
              rows="4"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
              required
            ></textarea>
          </div>
          <div className="mb-4">
            <label htmlFor="imageUrl" className="block text-sm font-semibold text-secondary mb-1">图片 URL</label>
            <input
              type="text"
              id="imageUrl"
              name="imageUrl"
              value={currentCourse.imageUrl}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>
          <div className="mb-4">
            <label htmlFor="price" className="block text-sm font-semibold text-secondary mb-1">价格 (¥)</label>
            <input
              type="number"
              id="price"
              name="price"
              value={currentCourse.price}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
              required
              step="0.01" // 允许小数点
            />
          </div>
          <div className="mb-6">
            <label htmlFor="baiduYunUrl" className="block text-sm font-semibold text-secondary mb-1">百度网盘链接</label>
            <input
              type="text"
              id="baiduYunUrl"
              name="baiduYunUrl"
              value={currentCourse.baiduYunUrl}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>
          <div className="text-right">
            <button
              type="button" // Type button to prevent form submission
              onClick={closeModal}
              className="mr-4 px-4 py-2 border border-primary text-primary rounded-md hover:bg-gray-100 transition duration-200"
            >
              取消
            </button>
            <button
              type="submit"
              className="bg-primary hover:bg-indigo-700 text-white font-bold py-2 px-6 rounded-md shadow-md transition duration-200 ease-in-out"
            >
              {modalMode === 'add' ? '添加课程' : '保存修改'}
            </button>
          </div>
        </form>
      </Modal>

    </div>
  );
};

export default AdminPage;