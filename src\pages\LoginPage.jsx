// src/pages/LoginPage.jsx
import React, { useState, useContext } from 'react'; // 引入 useContext
import { useNavigate, Link } from 'react-router-dom';
import { AuthContext } from '../AuthContext.jsx'; // 导入 AuthContext
import { login as apiLogin } from '../services/api'; // 从 api.js 引入 login 函数，并重命名以避免冲突

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login: contextLogin } = useContext(AuthContext); // 获取 context 中的 login 函数

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    try {
      // 调用 API 的 login 函数
      const response = await apiLogin(username, password);

      if (response.success) {
        // 登录成功，调用 context 的 login 来更新全局状态
        // 注意：这里的 contextLogin 是模拟的，因为 apiLogin 返回的是 success
        // 在实际应用中，apiLogin 应该返回 token 等信息
        const loginSuccess = await contextLogin(username, password); // contextLogin 是我们模拟的

        if (loginSuccess) {
          console.log("Login successful, navigating to admin.");
          navigate('/admin');
        } else {
          setError('登录失败，请检查用户名或密码。');
        }
      } else {
        setError(response.message || '登录失败，请检查用户名和密码。');
      }
    } catch (err) {
      console.error("Login error:", err);
      setError('发生网络错误，请稍后再试。');
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] py-12">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-sm border border-gray-200">
        <h2 className="text-3xl font-bold text-primary mb-6 text-center">管理员登录</h2>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-secondary text-sm font-semibold mb-2" htmlFor="username">
              用户名
            </label>
            <input
              type="text"
              id="username"
              className="shadow appearance-none border rounded w-full py-3 px-4 text-secondary leading-tight focus:outline-none focus:shadow-outline border-gray-300 focus:border-primary"
              placeholder="请输入用户名"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
          </div>
          <div className="mb-6">
            <label className="block text-secondary text-sm font-semibold mb-2" htmlFor="password">
              密码
            </label>
            <input
              type="password"
              id="password"
              className="shadow appearance-none border rounded w-full py-3 px-4 text-secondary mb-3 leading-tight focus:outline-none focus:shadow-outline border-gray-300 focus:border-primary"
              placeholder="请输入密码"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          {error && (
            <p className="text-red-500 text-xs italic mb-4">{error}</p>
          )}
          <div className="flex items-center justify-center">
            <button
              type="submit"
              className="bg-primary hover:bg-indigo-700 text-white font-bold py-3 px-6 rounded-lg shadow-md focus:outline-none focus:shadow-outline transition duration-200 ease-in-out w-full"
            >
              登录
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;