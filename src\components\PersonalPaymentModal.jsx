// 个人收款优化版本 - 对接新API
import React, { useState, useEffect } from 'react';
import Modal from './Modal';

const PersonalPaymentModal = ({ isOpen, onClose, course, onPaymentSuccess }) => {
  const [step, setStep] = useState(1); // 1: 收款码, 2: 上传凭证, 3: 等待审核, 4: 成功
  const [paymentProof, setPaymentProof] = useState(null);
  const [userInfo, setUserInfo] = useState({
    name: '',
    email: '',
    phone: '',
    paymentTime: '',
    amount: '',
    remark: ''
  });
  const [error, setError] = useState('');
  const [orderId, setOrderId] = useState('');
  const [orderDetails, setOrderDetails] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 创建订单
  const createOrder = async () => {
    try {
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resourceId: course.id,
          phone: userInfo.phone || '13800138000' // 默认手机号，实际应用中应该从用户输入获取
        })
      });

      const result = await response.json();

      if (result.id) {
        setOrderId(result.id);
        setOrderDetails(result);
        return result.id;
      } else {
        throw new Error(result.message || '创建订单失败');
      }
    } catch (error) {
      console.error('创建订单错误:', error);
      setError('创建订单失败，请重试');
      throw error;
    }
  };

  // 处理文件上传
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB限制
        setError('图片大小不能超过5MB');
        return;
      }
      
      if (!file.type.startsWith('image/')) {
        setError('请上传图片文件');
        return;
      }
      
      setPaymentProof(file);
      setError('');
    }
  };

  // 处理表单输入
  const handleInputChange = (field, value) => {
    setUserInfo(prev => ({ ...prev, [field]: value }));
  };

  // 提交支付凭证
  const handleSubmitProof = async () => {
    // 验证必填字段
    if (!paymentProof) {
      setError('请上传支付截图');
      return;
    }

    if (!userInfo.name || !userInfo.email || !userInfo.paymentTime || !userInfo.amount) {
      setError('请填写完整信息');
      return;
    }

    if (parseFloat(userInfo.amount) !== course.price) {
      setError(`支付金额应为 ¥${course.price}`);
      return;
    }

    if (!orderId) {
      setError('订单信息错误，请重新开始');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // 使用新的API接口上传支付凭证
      const response = await fetch(`/api/orders/${orderId}/payment-proof`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentProof: paymentProof.name // 这里应该是文件名，实际应用中可能需要先上传文件
        })
      });

      const result = await response.json();

      if (result.success || response.ok) {
        setStep(3);
        // 开始轮询审核状态
        startPollingStatus(orderId);
      } else {
        setError(result.message || '提交失败，请重试');
      }
    } catch (err) {
      console.error('提交支付凭证错误:', err);
      setError('网络错误，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 轮询审核状态
  const startPollingStatus = (orderId) => {
    const pollInterval = setInterval(async () => {
      try {
        // 使用新的API接口获取订单详情
        const response = await fetch(`/api/orders/${orderId}`);
        const result = await response.json();

        if (result.status === 'approved') {
          clearInterval(pollInterval);
          setStep(4);
          // 获取下载链接
          setTimeout(async () => {
            try {
              const downloadResponse = await fetch(`/api/verify-payment/${orderId}`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
              });
              const downloadResult = await downloadResponse.json();
              if (downloadResult.success) {
                onPaymentSuccess(downloadResult.downloadUrl);
              } else {
                onPaymentSuccess(result.downloadUrl || '下载链接获取失败');
              }
            } catch (err) {
              console.error('获取下载链接错误:', err);
              onPaymentSuccess('下载链接获取失败');
            }
          }, 2000);
        } else if (result.status === 'rejected') {
          clearInterval(pollInterval);
          setError(result.reason || '审核未通过，请重新提交');
          setStep(2);
        }
      } catch (err) {
        console.error('轮询状态错误:', err);
      }
    }, 5000); // 每5秒检查一次

    // 30分钟后停止轮询
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 30 * 60 * 1000);
  };

  // 初始化订单
  React.useEffect(() => {
    if (isOpen && !orderId && course) {
      // 当弹窗打开时自动创建订单
      createOrder().catch(err => {
        console.error('创建订单失败:', err);
        setError('创建订单失败，请重试');
      });
    }
  }, [isOpen, course]);

  const handleClose = () => {
    setStep(1);
    setPaymentProof(null);
    setUserInfo({
      name: '',
      email: '',
      phone: '',
      paymentTime: '',
      amount: '',
      remark: ''
    });
    setError('');
    setOrderId('');
    setOrderDetails(null);
    setIsSubmitting(false);
    onClose();
  };

  // 步骤1：显示收款码
  const renderStep1 = () => (
    <div className="text-center">
      <h3 className="text-xl font-semibold mb-4">微信转账支付</h3>

      <div className="mb-4">
        <p className="text-gray-600 mb-2">课程：{course?.title}</p>
        <p className="text-2xl font-bold text-blue-600">¥ {course?.price?.toFixed(2)}</p>
        {orderId && <p className="text-sm text-gray-500">订单号：{orderId}</p>}
        {!orderId && <p className="text-sm text-gray-500">正在创建订单...</p>}
      </div>

      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <p className="text-sm text-gray-600 mb-4">请使用微信扫描下方二维码转账</p>
        <div className="flex justify-center mb-4">
          <div className="w-64 h-64 bg-white p-2 rounded-lg shadow-md">
            <img 
              src="/wechat-qr.png" 
              alt="微信收款码" 
              className="w-full h-full object-contain rounded-lg"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'flex';
              }}
            />
            <div className="w-full h-full bg-green-500 rounded-lg flex items-center justify-center text-white" style={{display: 'none'}}>
              <div className="text-center">
                <div className="text-4xl mb-2">💳</div>
                <div className="text-sm">微信收款码</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-yellow-50 p-3 rounded-lg mb-4">
          <p className="text-sm text-yellow-800">
            ⚠️ 重要提示：
          </p>
          <ul className="text-xs text-yellow-700 mt-1 list-disc list-inside">
            <li>请务必转账 <strong>¥{course?.price?.toFixed(2)}</strong></li>
            <li>转账时请备注订单号：<strong>{orderId}</strong></li>
            <li>完成转账后请截图保存支付凭证</li>
          </ul>
        </div>
      </div>

      <div className="flex space-x-4">
        <button
          onClick={handleClose}
          className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button
          onClick={() => setStep(2)}
          className="flex-1 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
        >
          我已转账
        </button>
      </div>
    </div>
  );

  // 步骤2：上传支付凭证
  const renderStep2 = () => (
    <div>
      <h3 className="text-xl font-semibold mb-4 text-center">上传支付凭证</h3>
      
      <div className="space-y-4">
        {/* 上传支付截图 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            支付截图 <span className="text-red-500">*</span>
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {paymentProof && (
            <p className="text-sm text-green-600 mt-1">✓ 已选择文件：{paymentProof.name}</p>
          )}
        </div>

        {/* 用户信息 */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              姓名 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={userInfo.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              手机号
            </label>
            <input
              type="tel"
              value={userInfo.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            邮箱 <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            value={userInfo.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="用于接收下载链接"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              支付时间 <span className="text-red-500">*</span>
            </label>
            <input
              type="datetime-local"
              value={userInfo.paymentTime}
              onChange={(e) => handleInputChange('paymentTime', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              支付金额 <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              step="0.01"
              value={userInfo.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              placeholder={course?.price?.toFixed(2)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            备注
          </label>
          <textarea
            value={userInfo.remark}
            onChange={(e) => handleInputChange('remark', e.target.value)}
            placeholder="如有特殊说明请填写"
            rows="3"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {error && (
        <p className="text-red-500 text-sm mt-4">{error}</p>
      )}

      <div className="flex space-x-4 mt-6">
        <button
          onClick={() => setStep(1)}
          className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          返回
        </button>
        <button
          onClick={handleSubmitProof}
          disabled={isSubmitting}
          className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? '提交中...' : '提交审核'}
        </button>
      </div>
    </div>
  );

  // 步骤3：等待审核
  const renderStep3 = () => (
    <div className="text-center py-8">
      <div className="animate-pulse">
        <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
      <h3 className="text-xl font-semibold mb-4">审核中...</h3>
      <p className="text-gray-600 mb-4">
        我们正在验证您的支付信息，通常在5-30分钟内完成
      </p>
      <p className="text-sm text-gray-500">
        订单号：{orderId}
      </p>
    </div>
  );

  // 步骤4：审核通过
  const renderStep4 = () => (
    <div className="text-center py-8">
      <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
      <h3 className="text-xl font-semibold mb-4 text-green-600">支付验证成功！</h3>
      <p className="text-gray-600 mb-4">
        您的支付已确认，即将为您提供下载链接...
      </p>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="课程购买"
    >
      {step === 1 && renderStep1()}
      {step === 2 && renderStep2()}
      {step === 3 && renderStep3()}
      {step === 4 && renderStep4()}
    </Modal>
  );
};

export default PersonalPaymentModal;
